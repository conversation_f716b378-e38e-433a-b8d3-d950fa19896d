<template>
  <el-dialog
    :title="title"
    class="dialog-scenc"
    width="80%"
    :modal-append-to-body="false"
    @close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    destroy-on-close
    v-model="visible"
  >
    <div class="table-box">
      <div class="filter-container">
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'告警等级'"
          v-model="listQuery.alarmGrade"
        >
        </el-input>
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'告警名称'"
          v-model="listQuery.alarmName"
        >
        </el-input>
        <el-date-picker
          v-model="listQuery.listStartTime"
          type="datetime"
          size="mini"
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          placeholder="告警发生开始时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :disabled-date="disableStartDate"
        >
        </el-date-picker>
        <el-date-picker
          v-model="listQuery.listEndTime"
          type="datetime"
          size="mini"
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          placeholder="告警发生结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :disabled-date="disableEndDate"
        >
        </el-date-picker>
        <el-button
          class="filter-item search-btn"
          style="margin-left: 8px"
          size="mini"
          @click="handleFilter"
          >搜索</el-button
        >
        <el-button
        class="filter-item search-btn"
        style="margin-left: 8px"
        size="mini"
        @click="handleExport"
        :loading="exportLoading"
        >导出</el-button>
      </div>
      <el-table
        ref="mainTable"
        height="510"
        border
        fit
        :data="tableList"
        v-loading="listLoading"
        style="width: 100%"
        :row-class-name="tableRowClassName"
      >
        <!-- <el-table-column label="序号" type="index" width="80px" align="center">
          <template slot-scope="scope">
            <span>{{
              (listQuery.pageNo - 1) * listQuery.pageSize + scope.$index + 1
            }}</span>
          </template>
        </el-table-column> -->
        <el-table-column
          label="告警等级"
          min-width="120px"
          prop="alarmGrade"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="告警名称"
          min-width="150px"
          prop="alarmName"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="告警详情"
          min-width="200px"
          prop="alarmDetails"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="站址名称"
          min-width="150px"
          prop="siteName"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="告警发生时间"
          min-width="180px"
          prop="alarmTime"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="告警历时（分钟）"
          min-width="150px"
          prop="alarmLast"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="站址编码"
          min-width="150px"
          prop="siteCode"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="所属运营商"
          min-width="120px"
          prop="operator"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="所属市"
          min-width="120px"
          prop="city"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="所属区县"
          min-width="120px"
          prop="county"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="告警流水号ID"
          min-width="180px"
          prop="alarmWaterId"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="是否超时"
          min-width="100px"
          prop="isTimeout"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="告警来源"
          min-width="120px"
          prop="alarmFrom"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="故障单编号"
          min-width="150px"
          prop="faultCode"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="创建时间"
          min-width="180px"
          prop="updateTime"
          align="center"
          show-overflow-tooltip
        />
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="handleCurrentChange"
    />
  </el-dialog>
</template>

<script>
import { exportStationAlarm, getStationAlarmPage } from "@/api/sceneView/index";
export default {
  name: "top50Table",
  data() {
    return {
      title: "",
      visible: false,
      total: 0,
      listLoading: false,
      tableList: [],
      listQuery: {
        alarmGrade: "",         // 告警等级 (string)
        alarmName: "",          // 告警名称 (string)
        city: "",               // 市(无值为省级维度，有值为地市) (string)
        county: "",             // 区县 (string)
        endTime: "",            // 结束时间(Top50告警统计用) (string, date-time)
        faultCode: "",          // 故障单编号 (string)
        isAsc: "",              // 排序的方式 (desc 或者 asc) (string)
        listEndTime: "",        // 结束时间(清单列表用) (string, date-time)
        listStartTime: "",      // 开始时间(清单列表用) (string, date-time)
        operator: "",           // 所属运营商(不传为全量) (string)
        orderByColumn: "",      // 排序字段 (string)
        pageNum: 1,             // 页码 (integer, int32)
        pageSize: 20,           // 每页数量 (integer, int32)
        siteCode: "",           // 站址编码 (string)
        siteName: "",           // 站址名称 (string)
        startTime: "",          // 开始时间(Top50告警统计用) (string, date-time)
      },
      startTime: '',
      endTime: '',
      exportLoading: false
    };
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    async handleExport(){
      this.exportLoading = true;
      try {
        const blob = await exportStationAlarm({
          ...this.listQuery,
          startTime: this.startTime,
          endTime: this.endTime,
          pageSize: this.total
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.setAttribute('download', '单站告警TOP50.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(link.href);
      } catch (error) {
        console.error('下载附件失败:', error);
        this.$message.error('下载附件失败');
      }
      this.exportLoading = false;
    },
    handleOpen() {},
    handleCurrentChange(val) {
      this.listQuery.pageNum = val.page;
      this.listQuery.pageSize = val.limit;
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      let { result } = await getStationAlarmPage(this.listQuery);
      this.total = result.total;
      this.tableList = result.list;
      this.listLoading = false;
    },
    addRowColor(record, index) {
      return index % 2 != 0 ? "rowStyl" : "";
    },
    initForm(data) {
      this.title =  '单站告警TOP50'
      const { ...newData } = data
      this.startTime = newData.listStartTime
      this.endTime = newData.listEndTime
      this.listQuery = {
        ...this.listQuery,
        ...newData,
        pageNum: 1,
        pageSize: 20,
      }
      this.visible = true;
      this.getList();
    },
    handleFilter() {
      this.listQuery.pageNum = 1;
      this.getList();
    },
    close() {
      this.listQuery = {
        pageNum: 1,            // 页码
        pageSize: 20,          // 每页数量
      }
      this.listStartTime = ''
      this.listEndTime = ''
      this.visible = false;
    },
    tableRowClassName({ rowIndex }) {
      return rowIndex % 2 != 0 ? "rowStyle" : "";
    },
    handleClose() {
      this.close();
    },
    disableStartDate(time) {
      if (this.listQuery.listEndTime) {
        return time.getTime() > new Date(this.listQuery.listEndTime).getTime();
      }
      return false;
    },
    disableEndDate(time) {
      if (this.listQuery.listStartTime) {
        return time.getTime() < new Date(this.listQuery.listStartTime).getTime();
      }
      return false;
    }
  },
};
</script>

<style scoped lang="scss">
.filter-container {
  text-align: right;
  padding: 8px 10px;
  background-color: #446f86;
  :deep() .filter-item {
    margin-bottom: 0 !important;
  }
}
:deep() .el-input__wrapper {
  background-color: transparent;
  width: 100%;
  &.is-focus {
    box-shadow: 0 0 0 1px #059ec0;
  }
}
:deep() .el-input__inner {
  background-color: transparent;
  border: 0px solid #1296db;
  color: #82bee9;
}
.search-btn {
  background: #059ec0;
  color: #fff;
  border: 1px solid #059ec0;
  margin-top: -3px;
}
</style>

<style lang="scss">
.dialog-scenc {
  background-color: #065e89;
  opacity: 0.9;
  .el-dialog__header {
    height: 55px;
    line-height: 55px;
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid #059ec0;
    overflow: hidden;
    padding: 0 20px !important;
    .el-dialog__title {
      color: #fff;
    }
  }
  .el-dialog__body {
    padding: 10px 20px 0 20px;
    .rowStyle {
      background: #084969 !important;
    }
    .pagination-container {
      background: transparent;
      border-left: none;
      border-right: none;
      display: flex;
      justify-content: flex-end;
      .el-pagination__total {
        color: #fff;
      }
      .el-pagination__jump {
        color: #fff;
      }
    }
    .table-box {
      .sticky {
        background-color: rgba(144, 147, 153, 0.5);
        .el-input__inner {
          background: linear-gradient(0deg, #385fb866, #2238690d);
          border: 2px solid #059ec0;
          color: #82bee9;
          margin-bottom: 6px;
        }
        .search-btn {
          background: #059ec0;
          color: #fff;
          border: 1px solid #059ec0;
          margin-top: -3px;
        }
      }
      .el-table--enable-row-hover
        .el-table__body
        tr:hover
        > td.el-table__cell {
        background-color: rgba(133, 210, 249, 0.23922) !important;
      }
      .el-table td.el-table__cell {
        border-bottom: 1px solid #059ec0;
        color: #fff;
        height: 45px;
        font-size: 16px;
      }
      .el-table tr {
        background-color: transparent;
        height: 45px;
      }
      .el-table {
        background-color: transparent;
        &::before {
          height: 0;
        }
      }
      .el-table th.el-table__cell {
        background: #084969;
        color: #d2e7ff;
        font-size: 17px;
        font-weight: 700;
        border-bottom: 1px solid #059ec0;
      }
      .el-table__empty-text {
        color: #fff;
      }
      .el-table--border {
        border: 1px solid #059ec0;
      }
      .el-table--border .el-table__cell {
        border-right: 1px solid #059ec0;
      }
      .el-table--border::after {
        width: 0;
      }
      .el-table__body-wrapper {
        .el-table__body {
          width: 100% !important;
        }
        &::-webkit-scrollbar {
          width: 6px;
          height: 12px;
        }
        &::-webkit-scrollbar-thumb {
          // border-radius: 6px;
          background: rgba(144, 147, 153, 0.5);
          border-radius: 0;
          -webkit-box-shadow: inset 0 0 5px #0003;
        }
        &::-webkit-scrollbar-track {
          background: transparent;
          -webkit-box-shadow: inset 0 0 5px #0003;
          border-radius: 0;
        }
      }
    }
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: #fff;
  }
}
</style>
